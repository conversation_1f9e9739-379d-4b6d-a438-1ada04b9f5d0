import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { Grid, List, Filter, SortAsc } from 'lucide-react';

const Furniture = ({ products }) => {
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('name');
  const [priceRange, setPriceRange] = useState('all');

  // Filter products for Furniture category
  const furnitureProducts = products.filter(product => 
    product.category.toLowerCase() === 'furniture'
  );

  // Sort products
  const sortedProducts = [...furnitureProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'name':
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Filter by price range
  const filteredProducts = sortedProducts.filter(product => {
    switch (priceRange) {
      case 'under-100':
        return product.price < 100;
      case '100-500':
        return product.price >= 100 && product.price <= 500;
      case 'over-500':
        return product.price > 500;
      case 'all':
      default:
        return true;
    }
  });

  return (
    <div className="category-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">Home</Link>
          <span>/</span>
          <span>Furniture</span>
        </nav>

        {/* Category Header */}
        <div className="category-header">
          <div className="category-info">
            <h1>Furniture</h1>
            <p>Transform your space with our carefully selected furniture collection. From modern minimalist to classic designs, create the perfect ambiance for your home.</p>
            <div className="category-stats">
              <span>{filteredProducts.length} products found</span>
            </div>
          </div>
          <div className="category-image">
            <img 
              src="https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80" 
              alt="Furniture Category"
            />
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="products-controls">
          <div className="filters">
            <div className="filter-group">
              <label htmlFor="price-range">
                <Filter size={16} />
                Price Range
              </label>
              <select 
                id="price-range"
                value={priceRange} 
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="all">All Prices</option>
                <option value="under-100">Under $100</option>
                <option value="100-500">$100 - $500</option>
                <option value="over-500">Over $500</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sort-by">
                <SortAsc size={16} />
                Sort By
              </label>
              <select 
                id="sort-by"
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="name">Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Rating</option>
              </select>
            </div>
          </div>

          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <Grid size={20} />
            </button>
            <button 
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={20} />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className={`products-grid ${viewMode}`}>
          {filteredProducts.length > 0 ? (
            filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))
          ) : (
            <div className="no-products">
              <h3>No products found</h3>
              <p>Try adjusting your filters or browse all products.</p>
              <Link to="/products" className="btn-primary">
                View All Products
              </Link>
            </div>
          )}
        </div>

        {/* Category Features */}
        <div className="category-features">
          <h2>Why Choose Our Furniture?</h2>
          <div className="features-grid">
            <div className="feature">
              <h3>Quality Craftsmanship</h3>
              <p>Expertly crafted furniture built to last for years to come.</p>
            </div>
            <div className="feature">
              <h3>Modern Designs</h3>
              <p>Contemporary and timeless designs that complement any space.</p>
            </div>
            <div className="feature">
              <h3>Easy Assembly</h3>
              <p>Clear instructions and all necessary hardware included.</p>
            </div>
            <div className="feature">
              <h3>Free Delivery</h3>
              <p>Complimentary delivery and setup service for large furniture items.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Furniture;
