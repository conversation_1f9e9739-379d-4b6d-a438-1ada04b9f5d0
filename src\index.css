/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: 600ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-elastic: 800ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px var(--primary-color);
  }
  50% {
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
  100% {
    box-shadow: 0 0 5px var(--primary-color);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--white);
  margin: 0;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
}

/* Buttons */
button {
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: 500;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-3) var(--spacing-6);
  cursor: pointer;
  transition: all var(--transition-bounce);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgb(0 0 0 / 0.15);
}

button:active {
  transform: translateY(0) scale(0.98);
  animation: bounce 0.3s ease-out;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.btn-secondary:hover {
  background-color: var(--gray-300);
}

/* Form Elements */
input, textarea, select {
  font-family: inherit;
  font-size: var(--font-size-base);
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  transition: border-color var(--transition-fast);
  width: 100%;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

input.error, textarea.error, select.error {
  border-color: var(--error-color);
}

/* Layout */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  animation: fadeIn 0.8s ease-out;
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-6);
  }
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: var(--spacing-8);
  padding-bottom: var(--spacing-8);
}

/* Header Styles */
.header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
  animation: slideInLeft 0.8s ease-out;
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.header:hover {
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
  gap: var(--spacing-4);
  min-height: 70px;
}

.logo h1 {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0;
  animation: bounce 1s ease-out;
  transition: all var(--transition-bounce);
  cursor: pointer;
}

.logo h1:hover {
  transform: scale(1.1) rotate(-2deg);
  text-shadow: 0 4px 8px rgb(37 99 235 / 0.3);
  animation: pulse 1s infinite;
}

.search-form {
  flex: 1;
  max-width: 400px;
  width: 100%;
  animation: slideInLeft 0.6s ease-out;
  min-width: 250px;
}

.search-container {
  position: relative;
  display: flex;
  transition: transform var(--transition-bounce);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.search-container:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-xl), 0 0 30px rgb(37 99 235 / 0.2);
}

.search-input {
  flex: 1;
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  font-size: var(--font-size-base);
  min-height: 44px;
  transition: all var(--transition-smooth);
  position: relative;
  font-weight: 400;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1), 0 0 20px rgb(37 99 235 / 0.05);
  transform: translateY(-1px);
  animation: glow 2s infinite;
}

.search-input:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);
}

.search-input::placeholder {
  color: var(--gray-500);
  font-size: var(--font-size-lg);
  transition: color var(--transition-fast);
  font-weight: 400;
}

.search-input:focus::placeholder {
  color: var(--gray-400);
}

.search-button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-6) var(--spacing-8);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  cursor: pointer;
  transition: all var(--transition-bounce);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 72px;
  position: relative;
  overflow: hidden;
  min-width: 80px;
}

.search-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 8px 25px rgb(37 99 235 / 0.3);
  animation: pulse 1s infinite;
}

.search-button:active {
  transform: translateY(0) scale(0.98);
  animation: bounce 0.6s ease-out;
}

.search-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.search-button:hover::before {
  left: 100%;
}

.nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.nav-link {
  color: var(--gray-700);
  font-weight: 500;
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--gray-50);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgb(37 99 235 / 0.15);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 80%;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.icon-button, .cart-button {
  background: none;
  border: none;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.icon-button:hover, .cart-button:hover {
  color: var(--primary-color);
  background-color: var(--gray-50);
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--error-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  padding: var(--spacing-2);
  color: var(--gray-700);
  cursor: pointer;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
    gap: var(--spacing-4);
  }

  .search-form {
    order: 3;
    width: 100%;
    max-width: none;
  }

  .nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-top: 1px solid var(--gray-200);
    flex-direction: column;
    padding: var(--spacing-4);
    gap: var(--spacing-2);
  }

  .nav-open {
    display: flex;
  }

  .menu-toggle {
    display: block;
  }

  .user-actions {
    gap: var(--spacing-2);
  }
}

/* Footer Styles */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.footer-section h3, .footer-section h4 {
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.footer-section p {
  color: var(--gray-400);
  line-height: 1.6;
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--gray-800);
  color: var(--gray-400);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-2);
}

.footer-links a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--white);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: var(--gray-400);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-6);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-6);
}

.footer-bottom-links a {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.footer-bottom-links a:hover {
  color: var(--white);
}

@media (max-width: 768px) {
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
}

/* Product Card Styles */
.product-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-bounce);
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.6s ease-out;
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl), 0 20px 40px rgb(0 0 0 / 0.1);
  animation: float 3s ease-in-out infinite;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(37, 99, 235, 0.05), transparent);
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
}

.product-card:hover::before {
  opacity: 1;
}

.product-link {
  flex: 1;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
}

.product-image-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all var(--transition-elastic);
  filter: brightness(1) saturate(1);
}

.product-card:hover .product-image {
  transform: scale(1.1) rotate(1deg);
  filter: brightness(1.1) saturate(1.2);
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--gray-100);
  color: var(--gray-500);
  gap: var(--spacing-2);
}

.product-image-placeholder span {
  font-size: var(--font-size-sm);
  text-align: center;
}

.discount-badge {
  position: absolute;
  top: var(--spacing-3);
  left: var(--spacing-3);
  background-color: var(--error-color);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.wishlist-button {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  background-color: var(--white);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.wishlist-button:hover {
  background-color: var(--error-color);
  color: var(--white);
}

.product-info {
  padding: var(--spacing-4);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-category {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-2);
}

.product-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  line-height: 1.3;
  flex: 1;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: var(--gray-300);
}

.star.filled {
  color: var(--accent-color);
  fill: currentColor;
}

.star.half {
  color: var(--accent-color);
}

.rating-text {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.product-price {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.current-price {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.original-price {
  font-size: var(--font-size-base);
  color: var(--gray-500);
  text-decoration: line-through;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.feature-tag {
  background-color: var(--gray-100);
  color: var(--gray-700);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.product-actions {
  padding: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.add-to-cart-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.add-to-cart-btn:disabled {
  background-color: var(--gray-400);
  cursor: not-allowed;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

.products-grid .product-card:nth-child(1) { animation-delay: 0.1s; }
.products-grid .product-card:nth-child(2) { animation-delay: 0.2s; }
.products-grid .product-card:nth-child(3) { animation-delay: 0.3s; }
.products-grid .product-card:nth-child(4) { animation-delay: 0.4s; }
.products-grid .product-card:nth-child(5) { animation-delay: 0.5s; }
.products-grid .product-card:nth-child(6) { animation-delay: 0.6s; }
.products-grid .product-card:nth-child(7) { animation-delay: 0.7s; }
.products-grid .product-card:nth-child(8) { animation-delay: 0.8s; }
.products-grid .product-card:nth-child(9) { animation-delay: 0.9s; }
.products-grid .product-card:nth-child(10) { animation-delay: 1.0s; }

.products-grid.list {
  grid-template-columns: 1fr;
}

.products-grid.list .product-card {
  display: grid;
  grid-template-columns: 200px 1fr auto;
  height: auto;
}

.products-grid.list .product-image-container {
  aspect-ratio: 1;
}

.products-grid.list .product-link {
  display: contents;
}

.products-grid.list .product-info {
  padding: var(--spacing-4);
}

.products-grid.list .product-actions {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  border-top: none;
  border-left: 1px solid var(--gray-200);
}

.products-grid.list .add-to-cart-btn {
  width: auto;
  white-space: nowrap;
}

/* Product List Filters */
.product-list-container {
  max-width: 1200px;
  margin: 0 auto;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-6);
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.results-info h2 {
  margin-bottom: var(--spacing-2);
}

.results-info p {
  color: var(--gray-600);
  margin: 0;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.filter-toggle {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.filter-toggle:hover {
  background-color: var(--gray-200);
}

.view-mode-toggle {
  display: flex;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.view-btn {
  background-color: var(--white);
  color: var(--gray-600);
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.view-btn:hover:not(.active) {
  background-color: var(--gray-50);
}

.filters-panel {
  background-color: var(--gray-50);
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-6);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-6);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.filter-group label {
  font-weight: 600;
  color: var(--gray-700);
}

.price-range {
  display: flex;
  gap: var(--spacing-2);
}

.price-range input[type="range"] {
  flex: 1;
}

.no-products {
  text-align: center;
  padding: var(--spacing-16);
  color: var(--gray-500);
}

.no-products p {
  font-size: var(--font-size-lg);
}

/* Home Page Styles */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--white);
  padding: var(--spacing-20) 0;
  margin-bottom: var(--spacing-16);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: center;
}

.hero-text h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-6);
  line-height: 1.1;
}

.hero-text p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  opacity: 0.9;
}

.cta-button {
  background-color: var(--white);
  color: var(--primary-color);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-text h1 {
    font-size: var(--font-size-3xl);
  }
}

.features {
  padding: var(--spacing-16) 0;
  background-color: var(--gray-50);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
}

.feature {
  text-align: center;
  padding: var(--spacing-6);
}

.feature svg {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.feature h3 {
  margin-bottom: var(--spacing-3);
}

.feature p {
  color: var(--gray-600);
  margin: 0;
}

.categories-section {
  padding: var(--spacing-16) 0;
}

.categories-section h2 {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.category-card {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  aspect-ratio: 4/3;
  transition: all var(--transition-elastic);
  text-decoration: none;
  color: var(--white);
  animation: slideInRight 0.8s ease-out;
  box-shadow: var(--shadow-md);
}

.category-card:hover {
  transform: translateY(-8px) scale(1.05) rotate(1deg);
  box-shadow: var(--shadow-xl), 0 20px 40px rgb(0 0 0 / 0.2);
  animation: float 2s ease-in-out infinite;
}

.category-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all var(--transition-smooth);
  filter: brightness(0.8) saturate(1);
}

.category-card:hover img {
  transform: scale(1.1);
  filter: brightness(1) saturate(1.3);
}

.category-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--spacing-6) var(--spacing-4) var(--spacing-4);
}

.category-info h3 {
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-xl);
}

.category-info p {
  margin: 0;
  opacity: 0.9;
}

.featured-products {
  padding: var(--spacing-16) 0;
  background-color: var(--gray-50);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-12);
}

.view-all-link {
  color: var(--primary-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: gap var(--transition-fast);
}

.view-all-link:hover {
  gap: var(--spacing-2);
}

.newsletter {
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-16) 0;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  margin-bottom: var(--spacing-4);
}

.newsletter-content p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  gap: var(--spacing-3);
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
}

.newsletter-button {
  background-color: var(--white);
  color: var(--primary-color);
  border: none;
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.newsletter-button:hover {
  background-color: var(--gray-100);
}

@media (max-width: 768px) {
  .newsletter-form {
    flex-direction: column;
  }
}

/* Cart Page Styles */
.cart-page {
  min-height: 60vh;
}

.empty-cart {
  text-align: center;
  padding: var(--spacing-20);
}

.empty-cart svg {
  color: var(--gray-400);
  margin-bottom: var(--spacing-6);
}

.empty-cart h2 {
  margin-bottom: var(--spacing-4);
  color: var(--gray-600);
}

.empty-cart p {
  color: var(--gray-500);
  margin-bottom: var(--spacing-8);
}

.continue-shopping-btn {
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: background-color var(--transition-fast);
}

.continue-shopping-btn:hover {
  background-color: var(--primary-hover);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.continue-shopping {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 500;
}

.cart-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: var(--spacing-8);
  align-items: start;
}

@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
  }
}

.cart-items {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.cart-items-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

.cart-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  align-items: center;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.item-details h3 {
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-base);
}

.item-category {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

.item-features {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin: 0;
}

.item-price {
  font-weight: 600;
  color: var(--primary-color);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-1);
}

.quantity-controls button {
  background: none;
  border: none;
  padding: var(--spacing-1);
  cursor: pointer;
  color: var(--gray-600);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.quantity-controls button:hover:not(:disabled) {
  background-color: var(--gray-100);
  color: var(--primary-color);
}

.quantity-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.item-total {
  font-weight: 700;
  color: var(--gray-900);
}

.remove-btn {
  background: none;
  border: none;
  color: var(--error-color);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.remove-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

.cart-actions {
  padding: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.clear-cart-btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.clear-cart-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

.cart-summary {
  position: sticky;
  top: var(--spacing-20);
}

.summary-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
}

.summary-card h3 {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.summary-row.total {
  font-weight: 700;
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.summary-divider {
  height: 1px;
  background-color: var(--gray-200);
  margin: var(--spacing-4) 0;
}

.checkout-actions {
  margin-top: var(--spacing-6);
}

.checkout-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--font-size-lg);
  text-align: center;
  transition: background-color var(--transition-fast);
  margin-bottom: var(--spacing-3);
}

.checkout-btn:hover {
  background-color: var(--primary-hover);
}

.secure-checkout {
  text-align: center;
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  margin: 0;
}

.shipping-info {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--gray-50);
  border-radius: var(--radius-md);
  text-align: center;
}

.shipping-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Checkout Page Styles */
.checkout-page {
  min-height: 60vh;
}

.checkout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.back-link {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 500;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--spacing-8);
  align-items: start;
}

@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
  }
}

.checkout-form {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
}

.form-section {
  margin-bottom: var(--spacing-8);
}

.form-section h2 {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--gray-700);
}

.error-message {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.place-order-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  margin-top: var(--spacing-6);
}

.place-order-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.place-order-btn:disabled {
  background-color: var(--gray-400);
  cursor: not-allowed;
}

.order-summary {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: var(--spacing-20);
}

.order-items {
  margin-bottom: var(--spacing-6);
}

.order-item {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-200);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.order-item .item-details {
  flex: 1;
}

.order-item h4 {
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
}

.order-item p {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  margin: 0;
}

.order-item .item-price {
  font-weight: 600;
  color: var(--primary-color);
}

.order-totals {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-4);
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.total-row.total {
  font-weight: 700;
  font-size: var(--font-size-lg);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--gray-200);
}

.security-badges {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* Product Detail Page Styles */
.product-detail {
  padding: var(--spacing-8) 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-8);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

.breadcrumb a {
  color: var(--primary-color);
}

.breadcrumb span {
  color: var(--gray-400);
}

.product-not-found {
  text-align: center;
  padding: var(--spacing-20);
}

.product-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: start;
}

@media (max-width: 768px) {
  .product-detail-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
}

.product-images {
  position: sticky;
  top: var(--spacing-20);
}

.main-image {
  position: relative;
  aspect-ratio: 1;
  margin-bottom: var(--spacing-4);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-thumbnails {
  display: flex;
  gap: var(--spacing-3);
}

.thumbnail {
  width: 80px;
  height: 80px;
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.thumbnail.active {
  border-color: var(--primary-color);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-title {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.product-description {
  color: var(--gray-600);
  line-height: 1.7;
  margin-bottom: var(--spacing-6);
}

.product-features h4 {
  margin-bottom: var(--spacing-3);
}

.product-features ul {
  list-style: none;
  padding: 0;
}

.product-features li {
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-600);
}

.product-features li:last-child {
  border-bottom: none;
}

.quantity-selector {
  margin-bottom: var(--spacing-6);
}

.quantity-selector label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.add-to-cart-btn.primary {
  flex: 1;
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-4);
  font-size: var(--font-size-lg);
}

.wishlist-btn {
  background-color: var(--gray-100);
  color: var(--gray-700);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.wishlist-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

.product-guarantees {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-6);
}

.guarantee {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  color: var(--gray-600);
}

.guarantee:last-child {
  margin-bottom: 0;
}

/* About Page Styles */
.about-page {
  padding: var(--spacing-8) 0;
}

.about-hero {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.about-hero h1 {
  margin-bottom: var(--spacing-4);
}

.about-hero p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-story {
  margin-bottom: var(--spacing-16);
}

.about-story h2 {
  margin-bottom: var(--spacing-6);
}

.about-story p {
  color: var(--gray-600);
  line-height: 1.7;
  margin-bottom: var(--spacing-4);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-16);
  padding: var(--spacing-12) 0;
  background-color: var(--gray-50);
  border-radius: var(--radius-lg);
}

.stat {
  text-align: center;
  padding: var(--spacing-4);
}

.stat svg {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.stat h3 {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-2);
}

.stat p {
  color: var(--gray-600);
  margin: 0;
  font-weight: 500;
}

.about-values h2 {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
}

.value {
  text-align: center;
  padding: var(--spacing-6);
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.value h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.value p {
  color: var(--gray-600);
  line-height: 1.6;
  margin: 0;
}

/* Contact Page Styles */
.contact-page {
  padding: var(--spacing-8) 0;
}

.contact-hero {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.contact-hero h1 {
  margin-bottom: var(--spacing-4);
}

.contact-hero p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: start;
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
}

.contact-info h2 {
  margin-bottom: var(--spacing-8);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.contact-method {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.contact-method svg {
  color: var(--primary-color);
  margin-top: var(--spacing-1);
  flex-shrink: 0;
}

.contact-method h3 {
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.contact-method p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
}

.contact-method p:last-child {
  margin-bottom: 0;
}

.contact-form-container {
  background-color: var(--white);
  padding: var(--spacing-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.contact-form-container h2 {
  margin-bottom: var(--spacing-6);
}

.contact-form .form-group {
  margin-bottom: var(--spacing-4);
}

.contact-form label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--gray-700);
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  transition: border-color var(--transition-fast);
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.submit-btn {
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-4) var(--spacing-8);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.submit-btn:hover {
  background-color: var(--primary-hover);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .cart-items-header {
    display: none;
  }

  .cart-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
  }

  .item-info {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .quantity-controls {
    justify-self: start;
  }

  .checkout-content {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .products-grid.list .product-card {
    grid-template-columns: 1fr;
  }

  .products-grid.list .product-actions {
    border-left: none;
    border-top: 1px solid var(--gray-200);
  }
}

/* Profile Page */
.profile-page {
  padding: var(--spacing-6) 0;
  min-height: 80vh;
}

.profile-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: var(--spacing-8);
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .profile-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}

.profile-sidebar {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  height: fit-content;
  box-shadow: var(--shadow-sm);
}

.profile-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

@media (max-width: 768px) {
  .profile-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: var(--spacing-4);
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-base);
  color: var(--gray-600);
  text-align: left;
  width: 100%;
}

.nav-item:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
}

@media (max-width: 768px) {
  .nav-item {
    white-space: nowrap;
    min-width: fit-content;
  }
}

.profile-main {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-sm);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
}

.profile-info h2 {
  margin: 0 0 var(--spacing-2) 0;
  color: var(--gray-900);
}

.profile-email {
  color: var(--gray-600);
  margin: 0 0 var(--spacing-1) 0;
}

.profile-member {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  margin: 0;
}

.profile-actions {
  margin-left: auto;
}

@media (max-width: 768px) {
  .profile-actions {
    margin-left: 0;
  }
}

.edit-actions {
  display: flex;
  gap: var(--spacing-3);
}

.profile-details h3 {
  margin: 0 0 var(--spacing-6) 0;
  color: var(--gray-900);
  font-size: var(--font-size-lg);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.form-group p {
  margin: 0;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  color: var(--gray-900);
}

/* Orders Tab */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: var(--gray-50);
}

.order-info h4 {
  margin: 0 0 var(--spacing-1) 0;
  color: var(--gray-900);
}

.order-info p {
  margin: 0;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.order-status {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  margin-top: var(--spacing-1);
}

.order-status.delivered {
  background: var(--success-light);
  color: var(--success-color);
}

.order-status.shipped {
  background: var(--primary-light);
  color: var(--primary-color);
}

.order-status.processing {
  background: var(--warning-light);
  color: var(--warning-color);
}

.order-total {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

/* Settings Tab */
.settings-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.setting-item h4 {
  margin: 0;
  color: var(--gray-900);
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Category Pages */
.category-page {
  padding: var(--spacing-6) 0;
  min-height: 80vh;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.breadcrumb a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb span {
  color: var(--gray-400);
}

.category-header {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .category-header {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
  }
}

.category-info h1 {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--gray-900);
  font-size: var(--font-size-3xl);
}

.category-info p {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-4);
}

.category-stats {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.category-image {
  border-radius: var(--radius-md);
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.products-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-4);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .products-controls {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
}

.filters {
  display: flex;
  gap: var(--spacing-4);
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.filter-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
}

.filter-group select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  min-width: 150px;
}

.view-controls {
  display: flex;
  gap: var(--spacing-2);
}

.view-btn {
  padding: var(--spacing-2);
  border: 1px solid var(--gray-300);
  background: white;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.view-btn:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.view-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.no-products h3 {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--gray-900);
}

.no-products p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-6);
}

.category-features {
  margin-top: var(--spacing-12);
  padding: var(--spacing-8);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.category-features h2 {
  text-align: center;
  margin: 0 0 var(--spacing-8) 0;
  color: var(--gray-900);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.feature {
  text-align: center;
  padding: var(--spacing-4);
}

.feature h3 {
  margin: 0 0 var(--spacing-3) 0;
  color: var(--gray-900);
  font-size: var(--font-size-lg);
}

.feature p {
  color: var(--gray-600);
  line-height: 1.5;
  margin: 0;
}

/* Authentication Pages */
.auth-page {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) 0;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.auth-container {
  width: 100%;
  max-width: 480px;
  padding: 0 var(--spacing-4);
}

.auth-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-8);
  border: 1px solid var(--gray-200);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-header h1 {
  margin: 0 0 var(--spacing-2) 0;
  color: var(--gray-900);
  font-size: var(--font-size-3xl);
  font-weight: 700;
}

.auth-header p {
  margin: 0;
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-group label {
  font-weight: 500;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  padding-left: var(--spacing-12);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
  background: var(--white);
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-wrapper input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-wrapper input:disabled {
  background: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
}

.input-icon {
  position: absolute;
  left: var(--spacing-4);
  color: var(--gray-400);
  z-index: 1;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-4);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.password-toggle:hover {
  color: var(--gray-600);
  background: var(--gray-50);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.field-error {
  color: var(--error-color);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-lg);
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-2) 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.checkbox-wrapper input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.forgot-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: var(--transition-fast);
}

.forgot-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.auth-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  width: 100%;
  padding: var(--spacing-4);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  min-height: 48px;
}

.auth-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.auth-button:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

.auth-footer p {
  margin: 0;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
}

.auth-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.demo-credentials {
  margin-top: var(--spacing-6);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.demo-credentials h4 {
  margin: 0 0 var(--spacing-2) 0;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.demo-credentials p {
  margin: 0;
  color: var(--gray-600);
  font-size: var(--font-size-xs);
  font-family: monospace;
}

.demo-credentials small {
  display: block;
  margin-top: var(--spacing-2);
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

/* Password Strength Indicator */
.password-strength {
  margin-top: var(--spacing-3);
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.strength-fill {
  height: 100%;
  transition: var(--transition-normal);
  border-radius: 2px;
}

.strength-text {
  font-size: var(--font-size-xs);
  font-weight: 500;
  margin-bottom: var(--spacing-2);
}

.strength-requirements {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.requirement {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.requirement.met {
  color: var(--success-color);
}

.requirement svg {
  width: 14px;
  height: 14px;
  opacity: 0.3;
}

.requirement.met svg {
  opacity: 1;
}

/* Header Authentication Styles */
.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--gray-700);
  transition: var(--transition-fast);
  border: 1px solid var(--gray-200);
}

.user-profile:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.user-name {
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: none;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  color: var(--gray-600);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-xs);
}

.logout-button:hover {
  background: var(--gray-50);
  color: var(--gray-800);
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.login-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--gray-700);
  transition: var(--transition-fast);
  border: 1px solid var(--gray-200);
  font-size: var(--font-size-xs);
}

.login-button:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.signup-button {
  padding: var(--spacing-2) var(--spacing-4);
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 500;
  transition: var(--transition-fast);
}

.signup-button:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

/* Mobile Responsive for Auth */
@media (max-width: 768px) {
  .auth-page {
    padding: var(--spacing-4) 0;
  }

  .auth-card {
    padding: var(--spacing-6);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .user-info {
    display: none;
  }

  .auth-buttons {
    gap: var(--spacing-2);
  }

  .login-button span,
  .logout-button span {
    display: none;
  }

  .user-name {
    display: none;
  }
}

/* Auth Loading State */
.auth-loading {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-50);
}

.loading-container {
  text-align: center;
  padding: var(--spacing-8);
}

.loading-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4) auto;
}

.loading-container p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  margin: 0;
}