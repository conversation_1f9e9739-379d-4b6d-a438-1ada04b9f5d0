import React, { useState } from 'react';
import { User, Mail, Phone, MapPin, Edit, Save, X, Package, Heart, Settings } from 'lucide-react';

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [userInfo, setUserInfo] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'United States'
  });

  const [editedInfo, setEditedInfo] = useState(userInfo);

  const handleEdit = () => {
    setIsEditing(true);
    setEditedInfo(userInfo);
  };

  const handleSave = () => {
    setUserInfo(editedInfo);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedInfo(userInfo);
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setEditedInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'orders', label: 'Orders', icon: Package },
    { id: 'wishlist', label: 'Wishlist', icon: Heart },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const renderProfileTab = () => (
    <div className="profile-content">
      <div className="profile-header">
        <div className="profile-avatar">
          <User size={64} />
        </div>
        <div className="profile-info">
          <h2>{userInfo.firstName} {userInfo.lastName}</h2>
          <p className="profile-email">{userInfo.email}</p>
          <p className="profile-member">Member since January 2024</p>
        </div>
        <div className="profile-actions">
          {!isEditing ? (
            <button className="btn-primary" onClick={handleEdit}>
              <Edit size={16} />
              Edit Profile
            </button>
          ) : (
            <div className="edit-actions">
              <button className="btn-primary" onClick={handleSave}>
                <Save size={16} />
                Save
              </button>
              <button className="btn-secondary" onClick={handleCancel}>
                <X size={16} />
                Cancel
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="profile-details">
        <h3>Personal Information</h3>
        <div className="form-grid">
          <div className="form-group">
            <label>First Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
              />
            ) : (
              <p>{userInfo.firstName}</p>
            )}
          </div>
          <div className="form-group">
            <label>Last Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
              />
            ) : (
              <p>{userInfo.lastName}</p>
            )}
          </div>
          <div className="form-group">
            <label>Email</label>
            {isEditing ? (
              <input
                type="email"
                value={editedInfo.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
            ) : (
              <p>{userInfo.email}</p>
            )}
          </div>
          <div className="form-group">
            <label>Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            ) : (
              <p>{userInfo.phone}</p>
            )}
          </div>
        </div>

        <h3>Address Information</h3>
        <div className="form-grid">
          <div className="form-group full-width">
            <label>Address</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            ) : (
              <p>{userInfo.address}</p>
            )}
          </div>
          <div className="form-group">
            <label>City</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
              />
            ) : (
              <p>{userInfo.city}</p>
            )}
          </div>
          <div className="form-group">
            <label>State</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
              />
            ) : (
              <p>{userInfo.state}</p>
            )}
          </div>
          <div className="form-group">
            <label>ZIP Code</label>
            {isEditing ? (
              <input
                type="text"
                value={editedInfo.zipCode}
                onChange={(e) => handleInputChange('zipCode', e.target.value)}
              />
            ) : (
              <p>{userInfo.zipCode}</p>
            )}
          </div>
          <div className="form-group">
            <label>Country</label>
            {isEditing ? (
              <select
                value={editedInfo.country}
                onChange={(e) => handleInputChange('country', e.target.value)}
              >
                <option value="United States">United States</option>
                <option value="Canada">Canada</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="Australia">Australia</option>
              </select>
            ) : (
              <p>{userInfo.country}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderOrdersTab = () => (
    <div className="orders-content">
      <h3>Order History</h3>
      <div className="orders-list">
        <div className="order-item">
          <div className="order-info">
            <h4>Order #12345</h4>
            <p>Placed on March 15, 2024</p>
            <p className="order-status delivered">Delivered</p>
          </div>
          <div className="order-total">$129.99</div>
        </div>
        <div className="order-item">
          <div className="order-info">
            <h4>Order #12344</h4>
            <p>Placed on March 10, 2024</p>
            <p className="order-status shipped">Shipped</p>
          </div>
          <div className="order-total">$79.99</div>
        </div>
        <div className="order-item">
          <div className="order-info">
            <h4>Order #12343</h4>
            <p>Placed on March 5, 2024</p>
            <p className="order-status processing">Processing</p>
          </div>
          <div className="order-total">$199.99</div>
        </div>
      </div>
    </div>
  );

  const renderWishlistTab = () => (
    <div className="wishlist-content">
      <h3>My Wishlist</h3>
      <p>Your wishlist is empty. Start adding products you love!</p>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="settings-content">
      <h3>Account Settings</h3>
      <div className="settings-options">
        <div className="setting-item">
          <h4>Email Notifications</h4>
          <label className="toggle">
            <input type="checkbox" defaultChecked />
            <span className="slider"></span>
          </label>
        </div>
        <div className="setting-item">
          <h4>SMS Notifications</h4>
          <label className="toggle">
            <input type="checkbox" />
            <span className="slider"></span>
          </label>
        </div>
        <div className="setting-item">
          <h4>Marketing Emails</h4>
          <label className="toggle">
            <input type="checkbox" defaultChecked />
            <span className="slider"></span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileTab();
      case 'orders':
        return renderOrdersTab();
      case 'wishlist':
        return renderWishlistTab();
      case 'settings':
        return renderSettingsTab();
      default:
        return renderProfileTab();
    }
  };

  return (
    <div className="profile-page">
      <div className="container">
        <div className="profile-layout">
          <div className="profile-sidebar">
            <nav className="profile-nav">
              {tabs.map(tab => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    <Icon size={20} />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
          <div className="profile-main">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
