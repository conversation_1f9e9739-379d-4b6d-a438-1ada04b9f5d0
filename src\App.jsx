import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { CartProvider } from './context/CartContext';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import About from './pages/About';
import Contact from './pages/Contact';
import Profile from './pages/Profile';
import Electronics from './pages/Electronics';
import Clothing from './pages/Clothing';
import Furniture from './pages/Furniture';
import Sports from './pages/Sports';
import { products } from './data/products';

const App = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (term) => {
    setSearchTerm(term);
  };

  return (
    <CartProvider>
      <Router>
        <div className="app">
          <Header onSearch={handleSearch} searchTerm={searchTerm} />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Home products={products} />} />
              <Route path="/products" element={<Products products={products} searchTerm={searchTerm} />} />
              <Route path="/product/:id" element={<ProductDetail products={products} />} />
              <Route path="/cart" element={<Cart />} />
              <Route path="/checkout" element={<Checkout />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/electronics" element={<Electronics products={products} />} />
              <Route path="/clothing" element={<Clothing products={products} />} />
              <Route path="/furniture" element={<Furniture products={products} />} />
              <Route path="/sports" element={<Sports products={products} />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </CartProvider>
  );
};

export default App;
