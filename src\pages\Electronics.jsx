import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { Grid, List, Filter, SortAsc } from 'lucide-react';

const Electronics = ({ products }) => {
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('name');
  const [priceRange, setPriceRange] = useState('all');

  // Filter products for Electronics category
  const electronicsProducts = products.filter(product => 
    product.category.toLowerCase() === 'electronics'
  );

  // Sort products
  const sortedProducts = [...electronicsProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'name':
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Filter by price range
  const filteredProducts = sortedProducts.filter(product => {
    switch (priceRange) {
      case 'under-50':
        return product.price < 50;
      case '50-200':
        return product.price >= 50 && product.price <= 200;
      case 'over-200':
        return product.price > 200;
      case 'all':
      default:
        return true;
    }
  });

  return (
    <div className="category-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">Home</Link>
          <span>/</span>
          <span>Electronics</span>
        </nav>

        {/* Category Header */}
        <div className="category-header">
          <div className="category-info">
            <h1>Electronics</h1>
            <p>Discover the latest in technology and electronics. From smartphones to smart home devices, find everything you need to stay connected and productive.</p>
            <div className="category-stats">
              <span>{filteredProducts.length} products found</span>
            </div>
          </div>
          <div className="category-image">
            <img 
              src="https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80" 
              alt="Electronics Category"
            />
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="products-controls">
          <div className="filters">
            <div className="filter-group">
              <label htmlFor="price-range">
                <Filter size={16} />
                Price Range
              </label>
              <select 
                id="price-range"
                value={priceRange} 
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="all">All Prices</option>
                <option value="under-50">Under $50</option>
                <option value="50-200">$50 - $200</option>
                <option value="over-200">Over $200</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sort-by">
                <SortAsc size={16} />
                Sort By
              </label>
              <select 
                id="sort-by"
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="name">Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Rating</option>
              </select>
            </div>
          </div>

          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <Grid size={20} />
            </button>
            <button 
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={20} />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className={`products-grid ${viewMode}`}>
          {filteredProducts.length > 0 ? (
            filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))
          ) : (
            <div className="no-products">
              <h3>No products found</h3>
              <p>Try adjusting your filters or browse all products.</p>
              <Link to="/products" className="btn-primary">
                View All Products
              </Link>
            </div>
          )}
        </div>

        {/* Category Features */}
        <div className="category-features">
          <h2>Why Choose Our Electronics?</h2>
          <div className="features-grid">
            <div className="feature">
              <h3>Latest Technology</h3>
              <p>Stay ahead with the newest innovations and cutting-edge technology.</p>
            </div>
            <div className="feature">
              <h3>Quality Assured</h3>
              <p>All electronics come with manufacturer warranties and quality guarantees.</p>
            </div>
            <div className="feature">
              <h3>Expert Support</h3>
              <p>Get technical support and guidance from our electronics specialists.</p>
            </div>
            <div className="feature">
              <h3>Fast Shipping</h3>
              <p>Quick delivery on all electronics with secure packaging.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Electronics;
