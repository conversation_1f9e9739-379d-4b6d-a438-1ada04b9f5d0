import React, { useState } from 'react';
import { Image } from 'lucide-react';

const ImageWithFallback = ({ 
  src, 
  alt, 
  className = '', 
  placeholderSize = 48,
  loading = 'lazy',
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);

  if (imageError) {
    return (
      <div className={`product-image-placeholder ${className}`} {...props}>
        <Image size={placeholderSize} />
        <span>Image not available</span>
      </div>
    );
  }

  return (
    <img 
      src={src}
      alt={alt}
      className={className}
      loading={loading}
      onError={() => setImageError(true)}
      {...props}
    />
  );
};

export default ImageWithFallback;
