import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { Grid, List, Filter, SortAsc } from 'lucide-react';

const Clothing = ({ products }) => {
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('name');
  const [priceRange, setPriceRange] = useState('all');

  // Filter products for Clothing category
  const clothingProducts = products.filter(product => 
    product.category.toLowerCase() === 'clothing'
  );

  // Sort products
  const sortedProducts = [...clothingProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'name':
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Filter by price range
  const filteredProducts = sortedProducts.filter(product => {
    switch (priceRange) {
      case 'under-25':
        return product.price < 25;
      case '25-75':
        return product.price >= 25 && product.price <= 75;
      case 'over-75':
        return product.price > 75;
      case 'all':
      default:
        return true;
    }
  });

  return (
    <div className="category-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">Home</Link>
          <span>/</span>
          <span>Clothing</span>
        </nav>

        {/* Category Header */}
        <div className="category-header">
          <div className="category-info">
            <h1>Clothing</h1>
            <p>Express your style with our curated collection of fashion-forward clothing. From casual wear to formal attire, find pieces that reflect your personality.</p>
            <div className="category-stats">
              <span>{filteredProducts.length} products found</span>
            </div>
          </div>
          <div className="category-image">
            <img 
              src="https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80" 
              alt="Clothing Category"
            />
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="products-controls">
          <div className="filters">
            <div className="filter-group">
              <label htmlFor="price-range">
                <Filter size={16} />
                Price Range
              </label>
              <select 
                id="price-range"
                value={priceRange} 
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="all">All Prices</option>
                <option value="under-25">Under $25</option>
                <option value="25-75">$25 - $75</option>
                <option value="over-75">Over $75</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sort-by">
                <SortAsc size={16} />
                Sort By
              </label>
              <select 
                id="sort-by"
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="name">Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Rating</option>
              </select>
            </div>
          </div>

          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <Grid size={20} />
            </button>
            <button 
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={20} />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className={`products-grid ${viewMode}`}>
          {filteredProducts.length > 0 ? (
            filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))
          ) : (
            <div className="no-products">
              <h3>No products found</h3>
              <p>Try adjusting your filters or browse all products.</p>
              <Link to="/products" className="btn-primary">
                View All Products
              </Link>
            </div>
          )}
        </div>

        {/* Category Features */}
        <div className="category-features">
          <h2>Why Choose Our Clothing?</h2>
          <div className="features-grid">
            <div className="feature">
              <h3>Premium Quality</h3>
              <p>High-quality fabrics and materials for comfort and durability.</p>
            </div>
            <div className="feature">
              <h3>Trendy Styles</h3>
              <p>Stay fashionable with the latest trends and timeless classics.</p>
            </div>
            <div className="feature">
              <h3>Perfect Fit</h3>
              <p>Detailed size guides and flexible return policy for the perfect fit.</p>
            </div>
            <div className="feature">
              <h3>Sustainable</h3>
              <p>Eco-friendly materials and ethical manufacturing practices.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Clothing;
