import React from 'react';
import { Users, Award, Globe, Heart } from 'lucide-react';

const About = () => {
  return (
    <div className="about-page">
      <div className="container">
        <div className="about-hero">
          <h1>About ShopEase</h1>
          <p>Your trusted partner in online shopping since 2020</p>
        </div>

        <div className="about-content">
          <section className="about-story">
            <h2>Our Story</h2>
            <p>
              ShopEase was founded with a simple mission: to make online shopping easy, 
              affordable, and enjoyable for everyone. We believe that great products 
              shouldn't be hard to find or expensive to buy.
            </p>
            <p>
              Since our launch, we've helped thousands of customers find exactly what 
              they're looking for, from the latest electronics to everyday essentials. 
              Our commitment to quality, customer service, and competitive pricing has 
              made us a trusted name in e-commerce.
            </p>
          </section>

          <div className="about-stats">
            <div className="stat">
              <Users size={48} />
              <h3>50,000+</h3>
              <p>Happy Customers</p>
            </div>
            <div className="stat">
              <Award size={48} />
              <h3>1,000+</h3>
              <p>Quality Products</p>
            </div>
            <div className="stat">
              <Globe size={48} />
              <h3>25+</h3>
              <p>Countries Served</p>
            </div>
            <div className="stat">
              <Heart size={48} />
              <h3>99%</h3>
              <p>Customer Satisfaction</p>
            </div>
          </div>

          <section className="about-values">
            <h2>Our Values</h2>
            <div className="values-grid">
              <div className="value">
                <h3>Quality First</h3>
                <p>We carefully curate our product selection to ensure you get the best quality at competitive prices.</p>
              </div>
              <div className="value">
                <h3>Customer Focus</h3>
                <p>Your satisfaction is our priority. We're here to help you every step of the way.</p>
              </div>
              <div className="value">
                <h3>Innovation</h3>
                <p>We continuously improve our platform to provide you with the best shopping experience.</p>
              </div>
              <div className="value">
                <h3>Trust</h3>
                <p>We build lasting relationships with our customers through transparency and reliability.</p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default About;
