import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { Grid, List, Filter, SortAsc } from 'lucide-react';

const Sports = ({ products }) => {
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('name');
  const [priceRange, setPriceRange] = useState('all');

  // Filter products for Sports category
  const sportsProducts = products.filter(product => 
    product.category.toLowerCase() === 'sports'
  );

  // Sort products
  const sortedProducts = [...sportsProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'name':
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Filter by price range
  const filteredProducts = sortedProducts.filter(product => {
    switch (priceRange) {
      case 'under-30':
        return product.price < 30;
      case '30-100':
        return product.price >= 30 && product.price <= 100;
      case 'over-100':
        return product.price > 100;
      case 'all':
      default:
        return true;
    }
  });

  return (
    <div className="category-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">Home</Link>
          <span>/</span>
          <span>Sports</span>
        </nav>

        {/* Category Header */}
        <div className="category-header">
          <div className="category-info">
            <h1>Sports & Fitness</h1>
            <p>Achieve your fitness goals with our premium sports and fitness equipment. From yoga essentials to workout gear, find everything you need for an active lifestyle.</p>
            <div className="category-stats">
              <span>{filteredProducts.length} products found</span>
            </div>
          </div>
          <div className="category-image">
            <img 
              src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80" 
              alt="Sports Category"
            />
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="products-controls">
          <div className="filters">
            <div className="filter-group">
              <label htmlFor="price-range">
                <Filter size={16} />
                Price Range
              </label>
              <select 
                id="price-range"
                value={priceRange} 
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="all">All Prices</option>
                <option value="under-30">Under $30</option>
                <option value="30-100">$30 - $100</option>
                <option value="over-100">Over $100</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sort-by">
                <SortAsc size={16} />
                Sort By
              </label>
              <select 
                id="sort-by"
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="name">Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Rating</option>
              </select>
            </div>
          </div>

          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <Grid size={20} />
            </button>
            <button 
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={20} />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className={`products-grid ${viewMode}`}>
          {filteredProducts.length > 0 ? (
            filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))
          ) : (
            <div className="no-products">
              <h3>No products found</h3>
              <p>Try adjusting your filters or browse all products.</p>
              <Link to="/products" className="btn-primary">
                View All Products
              </Link>
            </div>
          )}
        </div>

        {/* Category Features */}
        <div className="category-features">
          <h2>Why Choose Our Sports Equipment?</h2>
          <div className="features-grid">
            <div className="feature">
              <h3>Professional Grade</h3>
              <p>High-quality equipment trusted by athletes and fitness enthusiasts.</p>
            </div>
            <div className="feature">
              <h3>Durable Materials</h3>
              <p>Built to withstand intense workouts and regular use.</p>
            </div>
            <div className="feature">
              <h3>Expert Guidance</h3>
              <p>Get fitness tips and equipment recommendations from our experts.</p>
            </div>
            <div className="feature">
              <h3>Quick Delivery</h3>
              <p>Fast shipping so you can start your fitness journey right away.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sports;
